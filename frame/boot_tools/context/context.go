package boot_context

import (
	"context"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/store"
	"github.com/gin-gonic/gin"
)

// boot_tools 结构体用于管理应用程序启动时的上下文信息，
// 包括根 Span、Gin 上下文、标准上下文以及存储对象。
type BootContext struct {
	rootSpan *span.RootSpan  // 根 Span，用于跟踪请求链
	ginCtx   *gin.Context    // Gin 上下文，用于处理 HTTP 请求
	ctx      context.Context // 标准上下文，用于控制请求的生命周期
	store    *store.BootStore // 存储对象，用于在启动过程中共享数据
}

func NewBootContext(rootName string) *BootContext {
	return &BootContext{
		rootSpan: span.NewRootSpan(rootName),
		store:    store.NewBootStore(),
		ctx:      context.TODO(),
	}
}


// GetGinCtx 返回当前 boot_tools 中的 Gin 上下文。
func (bc *BootContext) GetGinCtx() *gin.Context {
	return bc.ginCtx
}

// SetGinCtx 设置当前 boot_tools 中的 Gin 上下文。
// gc: 要设置的 Gin 上下文。
func (bc *BootContext) SetGinCtx(gc *gin.Context) {
	bc.ginCtx = gc
}

// RootSpan 返回当前 boot_tools 中的根 Span。
func (bc *BootContext) RootSpan() *span.RootSpan {
	return bc.rootSpan
}

// GetContext 返回当前 boot_tools 中的标准上下文。
func (bc *BootContext) GetContext() context.Context {
	return bc.ctx
}

// SetContext 设置当前 boot_tools 中的标准上下文。
// ctx: 要设置的标准上下文。
func (bc *BootContext) SetContext(ctx context.Context) {
	bc.ctx = ctx
}

// Store 返回当前 boot_tools 中的存储对象。
func (bc *BootContext) Store() *store.BootStore {
	return bc.store
}