package boot_chains

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
)

type BootChainsEngine struct {
	chains *chains_engine.MultiChainsEngine
}

func NewBootChainsEngine() *BootChainsEngine {
	return &BootChainsEngine{
		chains: chains_engine.NewMultiChainsEngine(),
	}
}

func (bc *BootChainsEngine) Register(chainName string, phs ...BootChainsHandler) *BootChainsEngine {
	bc.chains.Register(chainName, bootChainWrapper(phs...)...)
	return bc
}

func (bc *BootChainsEngine) ChainsLen(chainName string) int {
	chs := bc.chains.ChainsEngine(chainName)

	if chs == nil {
		return 0
	}

	return chs.ChainesLen()
}

func (bc *BootChainsEngine) ChainsEngine(chainName string) chains_engine.ChainsEngine {
	return bc.chains.ChainsEngine(chainName)
}
