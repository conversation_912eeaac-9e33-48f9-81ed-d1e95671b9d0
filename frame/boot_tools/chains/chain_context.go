package boot_chains

import (
	boot_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/context"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
)

type BootChainContext = chains_engine.ChainsContext

func NewContextFromBootContext(bctx *boot_context.BootContext) BootChainContext {
	bnc := chains_engine.NewChainsContextFromContext(bctx.GetContext())
	bnc.SetStore(BootCtxKey, bctx)
	return bnc
}
