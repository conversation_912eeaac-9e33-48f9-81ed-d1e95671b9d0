package boot_chains

import (
	boot_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/context"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
)

type BootChainsFunc func(ctx *boot_context.BootContext, span *span.Span) error

type BootChainsHandler struct {
	StepName string
	Handler  BootChainsFunc
}

func NewBootChainsHandler(stepName string, handler BootChainsFunc) BootChainsHandler {
	return BootChainsHandler{
		StepName: stepName,
		Handler:  handler,
	}
}

func bootChainWrapper(fns ...BootChainsHandler) []*chains_engine.ProcessHandler {

	fnReturn := make([]*chains_engine.ProcessHandler, 0, len(fns))

	for i, _ := range fns {
		fnReturn = append(fnReturn, &chains_engine.ProcessHandler{
			StepName: fns[i].StepName,
			Handler: func(ctx chains_engine.ChainsContext) {
				bootCtx := ctx.GetStoreWithDef(BootCtxKey, nil).(*boot_context.BootContext)
				span := bootCtx.RootSpan().AddSpan(ctx.StepName())
				defer span.Finish()

				err := fns[i].Handler(bootCtx, span)
				if err != nil {
					ctx.Abort(err)
					span.TraceInfo("error", err.Error())
				}
			},
		})
	}

	return fnReturn
}
