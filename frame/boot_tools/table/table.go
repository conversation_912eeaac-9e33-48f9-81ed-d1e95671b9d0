package bootcxt_table

import (
	"maps"
	"sync"
)

// Table 是一个线程安全的键值对存储结构，支持泛型。
type Table[V any] struct {
	lock sync.RWMutex `json:"-"` // 用于并发控制的读写锁
	data map[string]V `json:"data"` // 存储实际数据的映射
}

// NewTable 创建一个新的 Table 实例。
func NewTable[V any]() Table[V] {
	return Table[V]{
		lock: sync.RWMutex{},
		data: map[string]V{},
	}
}

// SetData 设置指定键的值，并返回当前 Table 实例。
func (s *Table[V]) SetData(k string, data V) *Table[V] {
	s.lock.Lock()
	defer s.lock.Unlock()

	s.data[k] = data
	return s
}

// SetUnique 设置指定键的值，如果键已存在则返回 false。
func (s *Table[V]) SetUnique(k string, data V) bool {
	s.lock.Lock()
	defer s.lock.Unlock()

	if _, ok := s.data[k]; ok {
		return false
	}

	s.data[k] = data

	return true
}

// DelData 删除指定键的值。
func (s *Table[V]) DelData(k string) {
	s.lock.Lock()
	defer s.lock.Unlock()

	delete(s.data, k)
	return
}

// CopyTable 复制当前 Table 实例，返回一个新的 Table 实例。
func (s *Table[V]) CopyTable() *Table[V] {
	s.lock.RLock()
	defer s.lock.RUnlock()

	nt := &Table[V]{
		lock: sync.RWMutex{},
		data: map[string]V{},
	}

	//map 拷贝
	maps.Copy(nt.data, s.data)
	return nt
}

// CopyData 复制当前 Table 中的数据，返回一个新的映射。
func (s *Table[V]) CopyData() map[string]V {
	s.lock.RLock()
	defer s.lock.RUnlock()
	
	return maps.Clone(s.data)
}

// GetData 获取指定键的值，并返回是否存在该键。
func (s *Table[V]) GetData(k string) (V, bool) {
	s.lock.RLock()
	defer s.lock.RUnlock()

	v, ok := s.data[k]
	return v, ok
}

// GetDefaultData 获取指定键的值，如果不存在则返回默认值。
func (s *Table[V]) GetDefaultData(k string, def V) V {
	s.lock.RLock()
	defer s.lock.RUnlock()

	if v, ok := s.data[k]; ok {
		return v
	}

	return def
}

// GetKeys 返回当前 Table 中所有键的列表。
func (s *Table[V]) GetKeys() []string {
	s.lock.RLock()
	defer s.lock.RUnlock()

	results := make([]string, 0, len(s.data))
	for k := range s.data {
		results = append(results, k)
	}
	return results
}