package span

import (
	"sync"
	"time"

	bootcxt_table "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/table"
)

// spanTags 是 Table 类型的别名，用于存储 Span 的标签数据。
type spanTags = bootcxt_table.Table[any]

// spanBase 是 Span 的基础结构体，包含 Span 的基本信息和操作。
type spanBase struct {
	baseLock  sync.RWMutex `json:"-"`         // 用于并发控制的读写锁
	parentId  string       `json:"parentId"`  // 父 Span 的 ID
	childs    []*Span      `json:"childs"`    // 子 Span 列表
	name      string       `json:"name"`      // Span 的名称
	startTime int64        `json:"startTime"` // Span 的开始时间
	endTime   int64        `json:"endTime"`   // Span 的结束时间
	elapse    int64        `json:"cost"`      // Span 的耗时（毫秒）
	tags      spanTags     `json:"tags"`      // Span 的标签数据
}

// newSpanBase 创建一个新的 spanBase 实例。
// name: Span 的名称。
func newSpanBase(name string) spanBase {
	return spanBase{
		baseLock:  sync.RWMutex{},
		parentId:  "",
		childs:    make([]*Span, 0, 32),
		name:      name,
		startTime: time.Now().UnixMilli(),
		endTime:   0,
		tags:      bootcxt_table.NewTable[any](),
	}
}

// Name 返回 Span 的名称。
func (spb spanBase) Name() string {
	return spb.name
}

func (spb *spanBase) ModifyName(name string)  {
	spb.name = name
}

// SetTag 设置 Span 的标签数据。
// k: 标签的键。
// v: 标签的值。
func (spb *spanBase) SetTag(k string, v any) {
	spb.tags.SetData(k, v)
}

// GetTag 获取 Span 的标签数据。
// k: 标签的键。
// 返回值: 标签的值和是否存在该标签。
func (spb *spanBase) GetTag(k string) (any, bool) {
	return spb.tags.GetData(k)
}

// GetDefaultTag 获取 Span 的标签数据，如果不存在则返回默认值。
// k: 标签的键。
// defv: 默认值。
// 返回值: 标签的值或默认值。
func (spb *spanBase) GetDefaultTag(k string, defv any) any {
	return spb.tags.GetDefaultData(k, defv)
}

// addChild 添加一个子 Span。
// spa: 要添加的子 Span。
func (spb *spanBase) addChild(spa *Span) {
	// 设置父 Span 的 ID
	spa.setParanetId(spb.name)

	spb.baseLock.Lock()
	defer spb.baseLock.Unlock()

	// 将子 Span 添加到子 Span 列表中
	spb.childs = append(spb.childs, spa)

	return
}

// setParanetId 设置父 Span 的 ID。
// parentName: 父 Span 的名称。
func (spb *spanBase) setParanetId(parentName string) {
	spb.baseLock.Lock()
	defer spb.baseLock.Unlock()

	spb.parentId = parentName
	return
}

// ChildSpans 返回所有子 Span。
// 返回值: 子 Span 列表。
func (spb *spanBase) ChildSpans() []*Span {
	spb.baseLock.RLock()
	defer spb.baseLock.RUnlock()

	result := make([]*Span, 0, len(spb.childs))
	result = append(result, spb.childs...)

	return result
}

// finish 标记 Span 的结束，并计算耗时。
func (spb *spanBase) finish() {
	spb.baseLock.Lock()
	defer spb.baseLock.Unlock()

	spb.endTime = time.Now().UnixMilli()
	spb.elapse = spb.endTime - spb.startTime
	return
}

// Cost 返回 Span 的耗时（毫秒）。
func (spb *spanBase) Cost() int64 {
	spb.baseLock.RLock()
	defer spb.baseLock.RUnlock()
	return spb.elapse
}

// copy 复制当前 spanBase 实例。
// 返回值: 新的 spanBase 实例。
func (spb *spanBase) copy() *spanBase {
	spb.baseLock.RLock()
	defer spb.baseLock.RUnlock()

	result := &spanBase{
		parentId:  spb.parentId,
		childs:    make([]*Span, len(spb.childs)),
		name:      spb.name,
		startTime: spb.startTime,
		endTime:   spb.endTime,
		elapse:    spb.elapse,
		tags:      *spb.tags.CopyTable(),
	}

	copy(result.childs, spb.childs)
	return result
}
