package span_option

type SpanOption struct {
	EnableMetrics   bool
	EnabelTimestamp bool
}

type SpanOptionFunc func(o *SpanOption)

func NewOptions() *SpanOption {
	return &SpanOption{
		EnableMetrics:   true,
		EnabelTimestamp: false,
	}
}

// 是否开启span的metrics
func Metrics(m bool) SpanOptionFunc {
	return func(o *SpanOption) {
		o.EnableMetrics = m
	}
}

// 是否开启span的metrics
func Timestamp(m bool) SpanOptionFunc {
	return func(o *SpanOption) {
		o.EnabelTimestamp = m
	}
}
