package span

import "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span/span_option"

type RootSpan struct {
	*Span
	ServerName string `json:"server_name"`
	Method     string `json:"method"`
	Path       string `json:"path"`
	Uri        string `json:"uri"`
}

func NewRootSpan(name string) *RootSpan {
	rs := &RootSpan{Method: "", Path: "", ServerName: ""}
	//root 节点自引用
	rs.Span = newSpan(name, rs, span_option.Timestamp(true))
	return rs
}

/*
func (rs *RootSpan) GetSpan(name string) (*Span, bool) {
	return rs.unique.GetData(name)
}
*/
