package span

import bootcxt_table "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/table"

// spanTraceTable 是 Table 类型的别名，用于存储 Span 的跟踪信息。
type spanTraceTable = bootcxt_table.Table[any]

// spanTraces 结构体包含 Span 的跟踪信息。
type spanTraces struct {
	stt spanTraceTable `json:"traces"` // 跟踪信息表
}

// TraceInfo 设置指定键的跟踪信息。
// k: 跟踪信息的键。
// v: 跟踪信息的值。
func (st *spanTraces) TraceInfo(k string, v any) {
	st.stt.SetData(k, v)
}

// copyTracesData 复制当前跟踪信息表中的数据，返回一个新的映射。
// 返回值: 包含所有跟踪信息的映射。
func (st *spanTraces) copyTracesData() map[string]any {
	return st.stt.CopyData()
}

// newSpanTrace 创建一个新的 spanTraces 实例。
// 返回值: 新的 spanTraces 实例。
func newSpanTrace() spanTraces {
	return spanTraces{
		stt: bootcxt_table.NewTable[any](),
	}
}