package span

import (
	bootcxt_metrics "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/metrics"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span/span_option"
)

// Span 结构体表示一个跟踪单元，包含基础信息、跟踪信息和根 Span 的引用。
type Span struct {
	spanBase
	spanTraces

	option *span_option.SpanOption `json:"-"`
	root   *RootSpan               `json:"-"` // 根 Span 的引用，用于获取全局标签信息
}

// newSpan 创建一个新的 Span 实例。
// name: Span 的名称。
// root: 根 Span 的引用。
// 返回值: 新的 Span 实例。
func newSpan(name string, root *RootSpan, fns ...span_option.SpanOptionFunc) *Span {
	option := span_option.NewOptions()
	for _, fn := range fns {
		fn(option)
	}

	spa := &Span{
		spanBase:   newSpanBase(name),
		root:       root,
		spanTraces: newSpanTrace(),
		option:     option,
	}

	// 获取默认的服务器、路径和方法标签
	if option.EnableMetrics {
		bootcxt_metrics.Global_metrics.StatsTotal(root.Path, root.Method, root.ServerName, name, 1)
	}

	return spa
}

// AddSpan 添加一个新的子 Span，并记录相关的统计信息。
// name: 子 Span 的名称。
// 返回值: 新创建的子 Span 实例。
func (s *Span) AddSpan(name string, fns ...span_option.SpanOptionFunc) *Span {
	cs := newSpan(name, s.root, fns...)
	s.addChild(cs)
	return cs
}

// Finish 标记 Span 的结束，并记录耗时统计信息。
func (s *Span) Finish() {
	s.finish()
	// 获取默认的服务器、路径和方法标签
	if s.option.EnableMetrics {
		bootcxt_metrics.Global_metrics.StatsElapse(s.root.Path, s.root.Method, s.root.ServerName, s.name, int(s.Cost()))
	}
}

// Finish 标记 Span 的结束，并记录耗时统计信息。
func (s *Span) Run(f func()) {
	f()
	s.Finish()
}

// JsonSpan 将 Span 转换为 JSON 格式的数据结构。
// 返回值: 转换后的 JsonSpan 实例。
func (spb *Span) JsonSpan() JsonSpan {
	if spb.name == "" && spb.startTime == 0 {
		return JsonSpan{}
	}

	// 复制当前 Span 的基础信息
	basecp := spb.copy()
	// 复制当前 Span 的跟踪信息
	traces := spb.copyTracesData()

	// 创建 JsonSpan 结构体实例，并填充相关字段
	js := JsonSpan{
		//ParentId:  basecp.parentId,         // 设置父 Span 的 ID
		Childs:    make([]JsonSpan, 0, 64), // 初始化子 Span 切片
		Name:      basecp.name,             // 设置 Span 名称
		Elpase:    basecp.elapse,           // 设置耗时
		TraceInfo: traces,                  // 设置跟踪信息
		Tags:      basecp.tags.CopyData(),  // 设置标签信息
	}

	if spb.option.EnabelTimestamp {
		js.StartTime = basecp.startTime // 格式化开始时间
		js.EndTime = basecp.endTime     // 设置结束时间
	}

	// 如果存在子 Span，则递归转换为 JsonSpan 并添加到 Childs 切片中
	if basecp.childs != nil {
		for k := range basecp.childs {
			js.Childs = append(js.Childs, basecp.childs[k].JsonSpan())
		}
	}

	return js
}
