package span

// span 为了安全性，不对外放开字段
type JsonSpan struct {
	//ParentId  string         `json:"parentId"`             // 父节点ID
	Childs    []JsonSpan     `json:"childs,omitempty"`     // 子节点列表
	Name      string         `json:"name"`                 // 节点名称
	StartTime int64          `json:"startTime,omitempty" ` // 开始时间
	EndTime   int64          `json:"endTime,omitempty"`    // 结束时间
	Elpase    int64          `json:"cost"`                 // 消耗时间（毫秒）
	Tags      map[string]any `json:"tags"`                 // 标签信息
	TraceInfo map[string]any `json:"traces"`               // 追踪信息
}
