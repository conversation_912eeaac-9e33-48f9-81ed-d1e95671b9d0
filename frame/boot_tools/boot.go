package boot_tools

import (
	boot_chains "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/chains"
	boot_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/context"
)

// Newboot_tools 创建一个新的 boot_tools 实例，并初始化根 Span 和存储对象。
// rootName: 根 Span 的名称，用于标识请求链的起点。
func NewBootContext(rootName string) *boot_context.BootContext {
	return boot_context.NewBootContext(rootName)
}


func NewBootChainsEngine() *boot_chains.BootChainsEngine {
	return boot_chains.NewBootChainsEngine()
}