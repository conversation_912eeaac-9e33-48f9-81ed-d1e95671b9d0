package bootcxt_metrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

// Metrics 定义了用于统计请求总数和请求延迟的接口
type Metrics interface {
	StatsTotal(path, method, server, span string, num int) Metrics     // 统计请求总数
	StatsElapse(path, method, server, span string, elapse int) Metrics // 统计请求延迟
}

// metrics 结构体实现了 Metrics 接口，包含请求总数和请求延迟的统计信息
type metrics struct {
	requestTotal *prometheus.CounterVec // 请求总数的计数器
	elapse       *prometheus.HistogramVec // 请求延迟的直方图
}

// Global_metrics 是全局的 Metrics 实例
var Global_metrics = NewMetrics()

// MetricsEnable 控制是否启用指标统计
var MetricsEnable = true

// NewMetrics 创建并返回一个新的 Metrics 实例
func NewMetrics() Metrics {
	m := &metrics{}

	// 定义请求总数的 CounterVec
	m.requestTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "span_total",
			Help: "Total number of span",
		},
		[]string{"module", "path", "server", "method", "span"}, // 定义标签
	)

	// 定义请求延迟的 HistogramVec 的桶范围
	buckets := []float64{1, 3, 5, 7, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100, 105, 110, 115, 120, 125, 130, 135, 140, 145, 150, 160, 170, 180, 190, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400, 420, 460, 480, 500, 520, 540, 560, 580, 600, 650, 700, 750, 800, 850, 900, 950, 1000, 1050, 1100, 1150, 1200, 1250, 1300, 1350, 1400, 1450, 1500, 1550, 1600, 1650, 1700, 1750, 1800, 1850, 1900, 1950, 2000, 2100, 2200, 2300, 2400, 2500, 2600, 2700, 2800, 2900, 3000, 3100, 3200, 3300, 3400, 3500, 3600, 3700, 3800, 3900, 4000, 4100, 4200, 4300, 4400, 4500, 4600, 4700, 4800, 4900, 5000, 5500, 6000, 6500, 7000, 7500, 8000, 8500, 9000, 9500, 10000}

	// 定义请求延迟的 HistogramVec
	m.elapse = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "span_elapse",
			Help:    "elapse millseconds of span",
			Buckets: buckets,
		},
		[]string{"module", "path", "server", "method", "span"},
	)

	// 注册指标到 prometheus
	prometheus.MustRegister(m.requestTotal, m.elapse)

	return m
}

// StatsTotal 统计请求总数
func (m *metrics) StatsTotal(path, method, server, span string, num int) Metrics {
	if MetricsEnable {
		m.requestTotal.With(prometheus.Labels{"module": "span", "path": path, "server": server, "method": method, "span": span}).Add(float64(num))
	}

	return m
}

// StatsElapse 统计请求延迟
func (m *metrics) StatsElapse(path, method, server, span string, elapse int) Metrics {
	if MetricsEnable {
		m.elapse.With(prometheus.Labels{"module": "span", "path": path, "server": server, "method": method, "span": span}).Observe(float64(elapse))
	}

	return m
}