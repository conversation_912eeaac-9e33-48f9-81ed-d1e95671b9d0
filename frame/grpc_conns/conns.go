package grpc_conns

import (
	"context"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/conns_pool"
	"google.golang.org/grpc"
)

type CallFn conns_pool.ConnCall[grpc.ClientConn]

type GrpcConnsPool struct {
	pool   *conns_pool.ConnPool[grpc.ClientConn]
	option *Option
}

func NewGrpcConnPool(fns ...OptionCall) (*GrpcConnsPool, error) {

	opt := newOption()
	for _, fn := range fns {
		fn(opt)
	}

	factory := newFactory(opt)
	cp, err := conns_pool.NewConnsPool(factory, opt.pfns...)

	if err != nil {
		return nil, err
	}

	return &GrpcConnsPool{
		pool: cp,
	}, nil
}

func (gpc *GrpcConnsPool) Call(ctx context.Context, target string, fn CallFn) (any, error) {
	return gpc.pool.Call(ctx, target, func(ctx context.Context, addr string, conn *grpc.ClientConn) (any, error) {
		return fn(ctx, addr, conn)
	})
}
