package grpc_conns

import (
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/conns_pool"
)

type OptionCall func(o *Option)

type Option struct {
	pfns     []conns_pool.OptionCall
	resolver Resolver
}

func newOption() *Option {
	return &Option{
		pfns:     make([]conns_pool.OptionCall, 0, 8),
		resolver: nil,
	}
}

func WithMaxLoadNumPerConn(num int) OptionCall {
	return func(o *Option) {
		o.pfns = append(o.pfns, conns_pool.WithMaxLoadNumPerConn(num))
	}
}

func WithMaxIdleNum(num int) OptionCall {
	return func(o *Option) {
		o.pfns = append(o.pfns, conns_pool.WithMaxIdleNum(num))
	}
}

func WithMaxConnNumPerHost(num int) OptionCall {
	return func(o *Option) {
		o.pfns = append(o.pfns, conns_pool.WithMaxConnNumPerHost(num))
	}
}

func WithMaxIdleTime(num time.Duration) OptionCall {
	return func(o *Option) {
		o.pfns = append(o.pfns, conns_pool.WithMaxIdleTime(num))
	}
}

func WithCleanInterval(num time.Duration) OptionCall {
	return func(o *Option) {
		o.pfns = append(o.pfns, conns_pool.WithCleanInterval(num))
	}
}

func WithResolver(r Resolver) OptionCall {
	return func(o *Option) {
		o.resolver = r
	}
}

func WithTlbResolver() OptionCall {
	return func(o *Option) {
		o.resolver = TlbResolver
	}
}
