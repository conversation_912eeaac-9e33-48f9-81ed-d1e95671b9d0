package grpc_conns

import (
	"context"
	"errors"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/tlb"
)

type Resolver func(ctx context.Context, target string) (string, error)

func TlbResolver(ctx context.Context, target string) (string, error) {
	var tinst *tlb.TlbSdk
	if tlb.G_tlb.DefaultInstance() == nil {
		return "", errors.New("tlb instance not init")
	}
	tinst = tlb.G_tlb.DefaultInstance()

	tag, ok := ctx.Value("tlb_tag").(string)

	if ok {
		info, err := tinst.GetBestServiceEx(ctx, target, tag)
		if err != nil {
			return "", err
		}

		return info.Addr(), nil
	}

	info, err := tinst.GetBestService(ctx, target)
	if err != nil {
		return "", err
	}

	return info.Addr(), nil
}
