package grpc_conns

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/conns_pool"
	"google.golang.org/grpc"
)

func isValidIPPort(addr string) bool {
	host, port, err := net.SplitHostPort(addr)
	if err != nil {
		return false // 解析失败，格式错误
	}

	// 检查 IP 是否有效
	if net.ParseIP(host) == nil {
		return false
	}

	// 解析端口号并检查范围
	portNum, err := strconv.Atoi(port)
	if err != nil || portNum < 0 || portNum > 65535 {
		return false
	}

	return true
}

func isDNSPrefixed(target string) bool {
	return strings.HasPrefix(target, "dns:///")
}

type connFactory struct {
	opt *Option
}

func newFactory(opt *Option) conns_pool.Factory[grpc.ClientConn] {
	return &connFactory{
		opt: opt,
	}
}

func (of *connFactory) Connect(ctx context.Context, target string) (*grpc.ClientConn, error) {

	//是否是IP或dns域名
	if isValidIPPort(target) || isDNSPrefixed(target) {
		return grpc.DialContext(ctx, target, grpc.WithInsecure())
	}

	if strings.HasPrefix(target, "unix://") {
		return grpc.DialContext(ctx, target, grpc.WithInsecure())
	}

	if of.opt.resolver != nil {
		addr, err := of.opt.resolver(ctx, target)
		if err != nil {
			return nil, err
		}

		return grpc.DialContext(ctx, addr, grpc.WithInsecure())
	}

	return nil, fmt.Errorf("can not reslover target %s", target)

}

func (of *connFactory) Close(target string, gc *grpc.ClientConn) {
	if gc != nil {
		gc.Close()
	}
	return
}
