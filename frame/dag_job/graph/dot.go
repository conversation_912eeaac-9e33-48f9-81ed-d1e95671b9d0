package graph

import (
	"fmt"
	"os"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/types"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/visualize"
	"gonum.org/v1/gonum/graph/encoding/dot"
	"gonum.org/v1/gonum/graph/simple"
)

func (graph *Graph) ToDot() {
	g := simple.NewDirectedGraph()

	nodes := graph.GetNodes()

	count := int64(0)
	mCnodes := map[string]*visualize.ColorNode{}
	for i, _ := range nodes {
		count++
		state := types.StatePending

		cnode := &visualize.ColorNode{
			IDVal: count,
			Name:  i,
			Color: visualize.StateToCorlor(state),
		}
		mCnodes[i] = cnode
		g.AddNode(cnode)
	}

	for i, v := range nodes {
		if nexts := v.GetNextNodes(); len(nexts) > 0 {
			for _, next := range nexts {
				edge := simple.Edge{F: mCnodes[i], T: mCnodes[next.GetName()]}
				g.<PERSON><PERSON>(edge)
			}
		}
	}
	// 生成DOT文件
	data, err := dot.Marshal(g, graph.name+"_Graph_DAG", "", "  ")
	if err != nil {
		panic(err)
	}

	err = os.WriteFile(graph.name+"_graph.dot", data, 0644)
	if err != nil {
		panic(err)
	}

	fmt.Printf("DOT 文件已保存为 %s_graph.dot，用 Graphviz 生成 PNG 看效果吧: dot -Tpng %s_graph.dot -o %s_graph.png\n", graph.name, graph.name, graph.name)
}
