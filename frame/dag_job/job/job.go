package job

import (
	"context"
	"errors"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/graph"
	job_result "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/result"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/runtime"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/task"
)

type Dag<PERSON>ob struct {
	name    string
	graph   *graph.Graph
	mTask   map[string]*task.Task
	isBuild bool
}

func NewDagJob(name string) *DagJob {
	return &DagJob{
		name:  name,
		graph: graph.NewGraph(name),
		mTask: map[string]*task.Task{},
	}
}

func (j *DagJob) Name() string {
	return j.name
}

func (j *DagJob) AddTask(t *task.Task) error {

	err := t.Validate()
	if err != nil {
		return err
	}

	err = j.graph.AddNode(t.Name)

	if err != nil {
		return err
	}

	j.mTask[t.Name] = t
	return nil
}

func (j *DagJob) AddEdge(from, to string) error {
	return j.graph.AddEdge(from, to)
}

// 验证DAG
func (j *DagJob) Build() error {
	//仅检查有无起始节点
	if len(j.graph.IndependentNodes()) == 0 {
		return errors.New("no start node: all node depended other node")
	}

	err := graph.HasCycle(j.graph.GetNodes())
	if err != nil {
		return err
	}

	j.isBuild = true
	return nil
}

func (j *DagJob) Run(ctx context.Context) *job_result.JobResult {
	if !j.isBuild {
		panic("dag job not build")
	}

	r := runtime.NewRuntime(j.name, j.graph, j.mTask)
	return r.Run(ctx)
}

func (j *DagJob) ToDot() {
	if !j.isBuild {
		panic("dag job not build")
	}

	j.graph.ToDot()
	return
}
