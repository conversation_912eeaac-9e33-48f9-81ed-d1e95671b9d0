package visualize

import (
	"fmt"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/types"
	"gonum.org/v1/gonum/graph/encoding"
)

// ColorNode 是一个支持 DOT 输出的节点
type ColorNode struct {
	IDVal int64
	Name  string
	Color string
}

// 实现 graph.Node 接口
func (n *ColorNode) ID() int64 {
	return n.IDVal
}

// 实现 dot.DOTIDer 接口（用于 DOT 图中节点的名字）
func (n *ColorNode) DOTID() string {
	return fmt.Sprintf("\"%s\"", n.Name)
}

// 实现 encoding.Attributer 接口（设置颜色、形状等属性）
func (n *ColorNode) Attributes() []encoding.Attribute {
	return []encoding.Attribute{
		{Key: "label", Value: fmt.Sprintf("\"%s\"", n.Name)},
		{Key: "style", Value: "\"filled\""},
		{Key: "fillcolor", Value: fmt.Sprintf("\"%s\"", n.Color)},
		{Key: "shape", Value: "\"box\""}, // 💡 关键在这里
	}
}

func StateToCorlor(state string) string {
	switch state {
	case types.StateFailed:
		return "red"
	case types.StateSuccess:
		return "green"
	case types.StateRunning:
		return "yellow"
	default:
		return "white"
	}
}
