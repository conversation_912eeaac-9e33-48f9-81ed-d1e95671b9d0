package runtime

import (
	"context"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/graph"
	job_result "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/result"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/runtime/state_machine"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/task"

	"github.com/panjf2000/ants/v2"
)

type Runtime struct {
	name  string
	graph *graph.Graph
	mTask map[string]*task.Task

	stateMachine *state_machine.StateMachine
}

var defaultAntsPool *ants.Pool

func init() {
	defaultAntsPool, _ = ants.NewPool(ants.DefaultAntsPoolSize)
}

func NewRuntime(name string, g *graph.Graph, mTask map[string]*task.Task) *Runtime {
	r := &Runtime{
		name:         name,
		graph:        g,
		mTask:        mTask,
		stateMachine: state_machine.NewStateMachine(name, g, mTask, defaultAntsPool),
	}

	return r
}

func (r *Runtime) Run(ctx context.Context) *job_result.JobResult {
	return r.stateMachine.Start(ctx)
}
