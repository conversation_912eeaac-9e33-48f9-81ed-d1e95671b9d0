package state_machine

import (
	"sync"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/types"
)

type state struct {
	start  time.Time
	end    time.Time
	err    error
	state  string
	rwlock sync.RWMutex
}

// NewState creates a new State with the given name and initial State.
func newState() *state {
	return &state{
		state:  types.StatePending,
		rwlock: sync.RWMutex{},
		err:    nil,
	}
}

// GetState returns the current State.
func (s *state) GetState() string {
	s.rwlock.RLock()
	defer s.rwlock.RUnlock()
	return s.state
}

func (s *state) GetStart() time.Time {
	s.rwlock.RLock()
	defer s.rwlock.RUnlock()
	return s.start
}

func (s *state) GetEnd() time.Time {
	s.rwlock.RLock()
	defer s.rwlock.RUnlock()
	return s.end
}

// SetState sets the State to the new State.
func (s *state) Success() {
	s.rwlock.Lock()
	defer s.rwlock.Unlock()

	// Check if the current State is not already StateSuccess or StateFailed
	if s.state == types.StatePending || s.state == types.StateRunning {
		s.state = types.StateSuccess
		s.end = time.Now()
	}
}

func (s *state) Running() {
	s.rwlock.Lock()
	defer s.rwlock.Unlock()

	if s.state == types.StatePending {
		s.state = types.StateRunning
		s.start = time.Now()
	}
}

func (s *state) Failed(err error) {
	s.rwlock.Lock()
	defer s.rwlock.Unlock()

	// Check if the current State is not already StateSuccess or StateFailed
	s.state = types.StateFailed
	s.end = time.Now()
	s.err = err
}

// IsPending checks if the State is pending.
func (s *state) IsPending() bool {
	s.rwlock.RLock()
	defer s.rwlock.RUnlock()
	return s.state == types.StatePending
}

// IsRunning checks if the State is running.
func (s *state) IsRunning() bool {
	s.rwlock.RLock()
	defer s.rwlock.RUnlock()
	return s.state == types.StateRunning
}

// IsFailed checks if the State is failed.
func (s *state) IsFailed() bool {
	s.rwlock.RLock()
	defer s.rwlock.RUnlock()
	return s.state == types.StateFailed
}

func (s *state) Error() error {
	s.rwlock.RLock()
	defer s.rwlock.RUnlock()
	return s.err
}

func (s *state) IsSuccess() bool {
	s.rwlock.RLock()
	defer s.rwlock.RUnlock()
	return s.state == types.StateSuccess
}
