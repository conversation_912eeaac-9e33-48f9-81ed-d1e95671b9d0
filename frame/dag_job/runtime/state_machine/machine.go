package state_machine

import (
	"context"

	graph_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/context"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/graph"
	job_result "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/result"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/runtime/singal"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/task"

	"github.com/panjf2000/ants/v2"
)

type StateMachine struct {
	name       string
	stateNodes map[string]*StateNode
	mTask      map[string]*task.Task
	graph      *graph.Graph
	antsPool   *ants.Pool
}

func NewStateMachine(name string, g *graph.Graph, mTask map[string]*task.Task, pool *ants.Pool) *StateMachine {
	sm := &StateMachine{
		name:       name,
		stateNodes: map[string]*StateNode{},
		mTask:      mTask,
		graph:      g,
		antsPool:   pool,
	}

	for name, node := range g.GetNodes() {
		sm.stateNodes[node.GetName()] = NewStateNode(name, mTask[name], node, sm)
	}

	return sm
}

func (e *StateMachine) Start(ctx context.Context) *job_result.JobResult {
	grpahCtx := graph_context.NewGraphContext(ctx)

	startNodes := e.graph.IndependentNodes()

	singal := singal.NewSingal(len(e.mTask))

	for name, _ := range startNodes {
		rn := e.stateNodes[name]
		singal.Add()
		err := e.antsPool.Submit(func() {
			rn.run(grpahCtx, singal)
		})

		if err != nil {
			panic(err)
		}
	}

	singal.Wait()

	results := job_result.NewJobResult(e.name, e.graph, e.mTask)
	for name, v := range e.stateNodes {
		jr := &job_result.NodeResult{
			Name:  name,
			State: v.State.GetState(),
			Start: v.State.GetStart().Format("2006-01-02 15:04:05.000"),
			End:   v.State.GetEnd().Format("2006-01-02 15:04:05.000"),
		}

		if v.State.Error() != nil {
			jr.Error = v.State.Error().Error()
		}

		results.NodesResult[name] = jr
	}

	return results
}
