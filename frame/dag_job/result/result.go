package job_result

import (
	"fmt"
	"os"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/graph"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/task"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/types"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/dag_job/visualize"
	"gonum.org/v1/gonum/graph/encoding/dot"
	"gonum.org/v1/gonum/graph/simple"
)

type NodeResult struct {
	Name  string
	State string
	Error string
	Start string
	End   string
}

type JobResult struct {
	DagJobName  string
	NodesResult map[string]*NodeResult
	graph       *graph.Graph
	tasks       map[string]*task.Task
}

func NewJobResult(name string, g *graph.Graph, mTask map[string]*task.Task) *JobResult {
	return &JobResult{
		DagJobName:  name,
		NodesResult: make(map[string]*NodeResult),
		graph:       g,
		tasks:       mTask,
	}
}

func (jr *JobResult) ToDot() {
	g := simple.NewDirectedGraph()

	nodes := jr.graph.GetNodes()

	count := int64(0)
	mCnodes := map[string]*visualize.ColorNode{}
	for i, _ := range nodes {
		count++
		state := types.StatePending

		sn, ok := jr.NodesResult[i]
		if ok {
			state = sn.State
		}

		cnode := &visualize.ColorNode{
			IDVal: count,
			Name:  i,
			Color: visualize.StateToCorlor(state),
		}
		mCnodes[i] = cnode
		g.AddNode(cnode)
	}

	for i, v := range nodes {
		if nexts := v.GetNextNodes(); len(nexts) > 0 {
			for _, next := range nexts {
				edge := simple.Edge{F: mCnodes[i], T: mCnodes[next.GetName()]}
				g.SetEdge(edge)
			}
		}
	}
	// 生成DOT文件
	data, err := dot.Marshal(g, jr.DagJobName+"_Result_DAG", "", "  ")
	if err != nil {
		panic(err)
	}

	err = os.WriteFile(jr.DagJobName+"_result.dot", data, 0644)
	if err != nil {
		panic(err)
	}

	fmt.Printf("DOT 文件已保存为 %s_result.dot，用 Graphviz 生成 PNG 看效果吧: dot -Tpng %s_result.dot -o %s_result.png\n", jr.DagJobName, jr.DagJobName, jr.DagJobName)
}
