package builder

// BuilderType 是一个泛型类型别名，表示任意类型。
type BuilderType = any

// Builder 是一个泛型结构体，用于构建和管理特定类型的构建器。
type Builder[T BuilderType] struct {
	builder *T
}

// GetBuilder 返回当前构建器的实例。
func (b *Builder[T]) GetBuilder() *T {
	return b.builder
}

// SetBuilder 设置当前构建器的实例，并返回设置后的构建器。
func (b *Builder[T]) SetBuilder(bt *T) *T {
	b.builder = bt
	return b.builder
}

// NewBuilder 创建一个新的 Builder 实例，并初始化其构建器。
func NewBuilder[T any](b *T) Builder[T] {
	return Builder[T]{
		builder: b,
	}
}

func NewBuilderPtr[T any](b *T) *Builder[T] {
	return &Builder[T]{
		builder: b,
	}
}