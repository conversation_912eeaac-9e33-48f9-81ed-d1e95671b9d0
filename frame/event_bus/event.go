package event_bus

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/async/better_pool"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/async/better_pool/model"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/async/feature"
)

type Consumer struct {
	topic     string
	consumerName string
	fn        func(any) (any,error)
	queuNum   int
	workerNum int
	pools     *better_pool.AsyncBetterPool
}

func NewConsumer(name string,concurrentNum,queueNum int,fn func(any) (any,error)) (*Consumer,error) {
	p,err := better_pool.NewBetterPool(&model.BetterPoolModel{
		MaxConcurrent: concurrentNum,
		QueueNum:queueNum,
		ExpireMs:1000,
		PreAlloc:false,
	})

	if err != nil {
		return nil,err
	}

	return &Consumer{
		consumerName: name,
		fn: fn,
		queuNum: queueNum,
		workerNum: concurrentNum,
		pools: p,
	},nil
}

func (cs *Consumer)consume(data any) (feature.AsyncFeatureAPI, error)  {
	return cs.pools.SubmitBlock(func() (any,error) {
		return cs.fn(data)
	})
}

func (cs *Consumer)close()  {
	cs.pools.Close()
}