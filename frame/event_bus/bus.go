package event_bus

import (
	"errors"
	"slices"
	"sync"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/async/feature"
)

type EvtBus struct {
	//topic -> Event
	rwlock sync.RWMutex
	close bool
	meb    map[string][]*Consumer
}


func NewEvtBus() *EvtBus {
	return &EvtBus{
		rwlock: sync.RWMutex{},
		close: false,
		meb: make(map[string][]*Consumer),
	}
}


func (evb *EvtBus) Subscribe(topic string,c *Consumer) error {
	if evb.IsClose() {
		return errors.New("event bus is closed")
	}

	evb.rwlock.Lock()
	defer evb.rwlock.Unlock()

	if _, ok := evb.meb[topic]; !ok {
		evb.meb[topic] = make([]*Consumer, 0)
	}

	evb.meb[topic] = append(evb.meb[topic], c)
	return nil
}


func (evb *EvtBus) Publish(topic string,data any) (map[string]feature.AsyncFeatureAPI,error) {
	if evb.IsClose() {
		return nil,errors.New("event bus is closed")
	}

	//这里即使上面的判断被越过了，也没关系。fixpool的close方法会保证不会panic
	cs := evb.getConsummers(topic)
	if cs == nil || len(cs) == 0 {
		return nil,errors.New("topic not found")
	}

	msub := make(map[string]feature.AsyncFeatureAPI)
	for _, c := range cs {
		f,err := c.consume(data)
		if err != nil {
			return nil,err
		}

		msub[c.consumerName] = f
	}

	return msub,nil
}


func (evb *EvtBus) IsClose() bool{
	evb.rwlock.RLock()
	defer evb.rwlock.RUnlock()
	return evb.close
}

func (evb *EvtBus) getConsummers(topic string) []*Consumer{
	evb.rwlock.RLock()
	defer evb.rwlock.RUnlock()

	if _, ok := evb.meb[topic]; !ok {
		return nil
	}

	return slices.Clone(evb.meb[topic])
}

func (evb *EvtBus) Close()  {
	evb.rwlock.Lock()
	defer evb.rwlock.Unlock()
	evb.close = true

	for _, cs := range evb.meb {
		for _, c := range cs {
			c.close()
		}
	}

	evb.meb = map[string][]*Consumer{}
}