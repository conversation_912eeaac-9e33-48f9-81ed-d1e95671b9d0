package boot_flow

import (
	"context"

	codeflow "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/code_flow"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/flow_context"
)

func NewCodeFlow() *codeflow.CodeFlow {
	return codeflow.NewCodeFlow()
}

func NewFlowContext(ctx context.Context) *flow_context.FlowContext {
	return flow_context.NewFlowContext(ctx)
}