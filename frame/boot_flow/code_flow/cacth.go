package code_flow

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/flow_context"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/builder"
)

type CatchExcuteFn = CodeExcuteFn

type catchExcute struct {
	bi *builder.Builder[CodeFlow]
	catchs  map[string]CatchExcuteFn
	general CatchExcuteFn
}

func newCatchExcute(p *CodeFlow) catchExcute {
	return catchExcute{
		bi: builder.NewBuilderPtr[CodeFlow](p),
		catchs:  nil,
		general: nil,
	}
}

func (c *catchExcute) Catch(err error, excute CatchExcuteFn) *CodeFlow {
	if c.catchs == nil {
		c.catchs = make(map[string]CatchExcuteFn)
	}
	c.catchs[err.Error()] = excute

	return c.bi.GetBuilder()
}

func (c *catchExcute) Error(excute CatchExcuteFn) *CodeFlow {
	c.general = excute
	return c.bi.GetBuilder()
}


func (c *catchExcute) excute(fc *flow_context.FlowContext)  {
	//err nil 直接返回
	if fc.Error() == nil {
		return 
	}

	//有无命中catch
	if f,ok := c.catchs[fc.Error().Error()];ok {
		f(fc)
		return
	}

	//命中general
	if c.general != nil {
		c.general(fc)
		return
	}
}