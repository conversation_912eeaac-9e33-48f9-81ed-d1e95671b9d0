package code_flow

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/flow_context"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/builder"
)

type FinalExcuteFn = CodeExcuteFn

type finalExcute struct {
	bi *builder.Builder[CodeFlow]
	fn FinalExcuteFn
}

func newFinalExcute(p *CodeFlow) finalExcute {
	return finalExcute{
		bi: builder.NewBuilderPtr[CodeFlow](p),
	}
}

func (p *finalExcute) Final(fn FinalExcuteFn) *CodeFlow {
	p.fn = fn
	return p.bi.GetBuilder()
}

func (p *finalExcute) excute(fc *flow_context.FlowContext) {
	if p.fn != nil {
		p.fn(fc)
	}
}