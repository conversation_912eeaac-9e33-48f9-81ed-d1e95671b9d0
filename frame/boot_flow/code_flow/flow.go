package code_flow

import (
	"context"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/flow_context"
)

type CodeExcuteFn func(fc *flow_context.FlowContext)

type CodeFlow struct {
	catchExcute
	thenExcute
	finalExcute
	recoverExcute
}

func NewCodeFlow() *CodeFlow {
	ffe := &CodeFlow{}

	ffe.catchExcute = newCatchExcute(ffe)
	ffe.thenExcute = newThenExcute(ffe)
	ffe.finalExcute = newFinalExcute(ffe)
	ffe.recoverExcute = newRecoverExcute(ffe)
	return ffe
}

func (ffe *CodeFlow) Excute(fc *flow_context.FlowContext) {
	defer func() {
		if ffe.recoverExcute.fn != nil {
			if r := recover(); r != nil {
				ffe.recoverExcute.excute(fc, r)
			}
		}
	}()

	defer func() {
		ffe.finalExcute.excute(fc)
	}()

    //执行then
	ffe.thenExcute.excute(fc)

	//执行catch error
	ffe.catchExcute.excute(fc)
}

func (ffe *CodeFlow) ExcuteSimple() (fc *flow_context.FlowContext) {
	fc = flow_context.NewFlowContext(context.TODO())
	ffe.Excute(fc)
	return
}