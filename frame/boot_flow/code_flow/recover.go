package code_flow

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/flow_context"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/builder"
)

type RecoverExcuteFn func(fc *flow_context.FlowContext,recover any) 

type recoverExcute struct {
	bi *builder.Builder[CodeFlow]
	fn RecoverExcuteFn
}

func newRecoverExcute(p *CodeFlow) recoverExcute {
	return recoverExcute{
		bi: builder.NewBuilderPtr[CodeFlow](p),
	}
}

func (p *recoverExcute) Recover(fn RecoverExcuteFn) *CodeFlow {
	p.fn = fn
	return p.bi.GetBuilder()
}

func (p *recoverExcute) excute(fc *flow_context.FlowContext,recover any) {
	if p.fn != nil {
		p.fn(fc,recover)
	}
}