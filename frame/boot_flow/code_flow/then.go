package code_flow

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/flow_context"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/builder"
)

type ThenExcuteFn = CodeExcuteFn

type thenExcute struct {
	bi *builder.Builder[CodeFlow]
	thens []ThenExcuteFn
}

func newThenExcute(p *CodeFlow) thenExcute {
	return thenExcute{
		bi: builder.NewBuilderPtr(p),
		thens: make([]ThenExcuteFn, 0, 8),
	}
}

func (t *thenExcute) Then(f ThenExcuteFn) *CodeFlow {
	t.thens = append(t.thens, f)
	return t.bi.GetBuilder()
}


func (t *thenExcute) excute(fc *flow_context.FlowContext) {
	for _, f := range t.thens {
		f(fc)
		
		if fc.Error() != nil || fc.IsAbort(){
			break
		}
	}
}