package flow_wrapper

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/code_flow"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_flow/flow_context"
)

func CondError(cond bool, err error) code_flow.CodeExcuteFn {
	return func(fc *flow_context.FlowContext) {
		fc.Throw(bif.Error(cond, err))
	}
}

func FuncError(f func() error) code_flow.CodeExcuteFn {
	return func(fc *flow_context.FlowContext) {
		fc.Throw(f())
	}
}

func NeedError(f func(...string) error, name ...string) code_flow.CodeExcuteFn {
	return func(fc *flow_context.FlowContext) {
		fc.Throw(f(name...))
	}
}
