package flow_context

import (
	"context"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
)

type FlowContext struct {
	ctx    context.Context
	store  map[any]any
	err    error
	abort  bool
	parent any
}

func NewFlowContext(ctx context.Context) *FlowContext {
	return &FlowContext{
		ctx:   ctx,
		store: map[any]any{},
		err:   nil,
		abort: false,
	}
}

func (c *FlowContext) Context() context.Context {
	return c.ctx
}

func (c *FlowContext) Set(key any, value any) {
	c.store[key] = value
}

func (c *FlowContext) Get(key any) any {
	if bif.InterfaceIsNil(c.store[key]) {
		return nil
	}
	return c.store[key]
}

func (c *FlowContext) Del(key any) {
	delete(c.store, key)
}

func (c *FlowContext) Store() map[any]any {
	return c.store
}

func (c *FlowContext) Throw(err error) {
	c.err = err
}

func (c *FlowContext) Abort() {
	c.abort = true
}

func (c *FlowContext) Error() error {
	if bif.InterfaceIsNil(c.err) {
		return nil
	}
	return c.err
}

func (c *FlowContext) IsAbort() bool {
	return c.abort
}
func (c *FlowContext) Parent() any {
	if bif.InterfaceIsNil(c.parent) {
		return nil
	}
	return c.parent
}

func (c *FlowContext) SetParent(parent any) {
	c.parent = parent
}
