package base_module

import (
	"errors"

	base_factory "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/factory"
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/internal/pkg"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/internal/shares"
)

// BootModule 是一个泛型模块结构体，用于管理模块的实例和配置。
type BootModule[INSTANCE any, CONFIG base_model.BaseModelAPI] struct {
	moduleName string
}

// NewBootModule 创建一个新的 BootModule 实例。
func NewBootModule[INSTANCE any, CONFIG base_model.BaseModelAPI](factory base_factory.FactoryAPI[INSTANCE, CONFIG]) *BootModule[INSTANCE, CONFIG] {
	return &BootModule[INSTANCE, CONFIG]{
		moduleName: factory.ModuleName(),
	}
}

// getModuleItem 根据名称获取模块中的具体项。
// 参数 name 是需要获取的项的名称。
// 返回值 *pkg.Pkg 是找到的包，bool 表示是否找到。
func (bm *BootModule[INSTANCE, CONFIG]) getModuleItem(name string) (*pkg.Pkg, bool) {
	modules := shares.GetModule(bm.moduleName)
	if modules == nil {
		return nil, false
	}
	item, ok := modules[name]
	return item, ok
}

// GetInstance 根据名称获取模块中的实例。
// 参数 name 是需要获取的实例的名称。
// 返回值 *INSTANCE 是找到的实例，如果未找到则返回 nil。
func (bm *BootModule[INSTANCE, CONFIG]) GetInstance(name string) *INSTANCE {
	if item, ok := bm.getModuleItem(name); ok {
		return item.Instance().(*INSTANCE)
	}
	return nil
}

// GetAllInstance 获取模块中的所有实例。
// 返回值 []*INSTANCE 是模块中所有实例的列表。
func (bm *BootModule[INSTANCE, CONFIG]) GetAllInstance() []*INSTANCE {
	modules := shares.GetModule(bm.moduleName)
	res := make([]*INSTANCE, 0, len(modules))
	for _, pkg := range modules {
		res = append(res, pkg.Instance().(*INSTANCE))
	}
	return res
}

// GetConfig 根据名称获取模块中的配置。
// 参数 name 是需要获取的配置的名称。
// 返回值 *CONFIG 是找到的配置，如果未找到则返回 nil。
func (bm *BootModule[INSTANCE, CONFIG]) GetConfig(name string) *CONFIG {
	if item, ok := bm.getModuleItem(name); ok {
		return item.Config().(*CONFIG)
	}
	return nil
}

// GetAllConfig 获取模块中的所有配置。
// 返回值 []*CONFIG 是模块中所有配置的列表。
func (bm *BootModule[INSTANCE, CONFIG]) GetAllConfig() []*CONFIG {
	modules := shares.GetModule(bm.moduleName)
	res := make([]*CONFIG, 0, len(modules))
	for _, pkg := range modules {
		res = append(res, pkg.Config().(*CONFIG))
	}
	return res
}

// DefaultConfig 获取模块中的默认配置。
// 返回值 *CONFIG 是默认配置，如果未找到则返回 nil。
func (bm *BootModule[INSTANCE, CONFIG]) DefaultConfig() *CONFIG {
	if item, ok := bm.getModuleItem("default"); ok {
		return item.Config().(*CONFIG)
	}
	for _, pkg := range shares.GetModule(bm.moduleName) {
		return pkg.Config().(*CONFIG)
	}
	return nil
}

// DefaultInstance 获取模块中的默认实例。
// 返回值 *INSTANCE 是默认实例，如果未找到则返回 nil。
func (bm *BootModule[INSTANCE, CONFIG]) DefaultInstance() *INSTANCE {
	if item, ok := bm.getModuleItem("default"); ok {
		return item.Instance().(*INSTANCE)
	}
	for _, pkg := range shares.GetModule(bm.moduleName) {
		return pkg.Instance().(*INSTANCE)
	}
	return nil
}

// Pkgs 获取模块中所有包的名称。
// 返回值 []string 是模块中所有包的名称列表。
func (bm *BootModule[INSTANCE, CONFIG]) Pkgs() []string {
	modules := shares.GetModule(bm.moduleName)
	results := make([]string, 0, len(modules))
	for _, v := range modules {
		results = append(results, v.IndexName())
	}
	return results
}

// ModuleName 获取模块的名称。
// 返回值 string 是模块的名称。
func (bm *BootModule[INSTANCE, CONFIG]) ModuleName() string {
	return bm.moduleName
}

// Must 检查模块中是否存在指定的包，如果不存在则 panic。
// 参数 names 是需要检查的包名称列表。
func (bm *BootModule[INSTANCE, CONFIG]) Must(names ...string) {
	modules := shares.GetModule(bm.moduleName)
	if modules == nil || len(modules) == 0 {
		panic("must module not found:" + bm.moduleName)
	}
	for _, name := range names {
		if _, ok := modules[name]; !ok {
			panic("must pkg not find:" + bm.moduleName + "." + name)
		}
	}
}

// Need 检查模块中是否存在指定的包，如果不存在则返回错误。
// 参数 names 是需要检查的包名称列表。
// 返回值 error 表示检查结果，如果所有包都存在则返回 nil，否则返回相应的错误信息。
func (bm *BootModule[INSTANCE, CONFIG]) Need(names ...string) error {
	modules := shares.GetModule(bm.moduleName)
	if modules == nil || len(modules) == 0 {
		return errors.New("need module not found:" + bm.moduleName)
	}
	for _, name := range names {
		if _, ok := modules[name]; !ok {
			return errors.New("need pkg not find:" + bm.moduleName + "." + name)
		}
	}
	return nil
}

// Enable 检查模块中是否存在指定的包，如果存在则返回 true，否则返回 false。
// 参数 names 是需要检查的包名称列表。
// 返回值 bool 表示检查结果，如果所有包都存在则返回 true，否则返回 false。
func (bm *BootModule[INSTANCE, CONFIG]) Enable(names ...string) bool {
	modules := shares.GetModule(bm.moduleName)
	if modules == nil || len(modules) == 0 {
		return false
	}
	for _, name := range names {
		if _, ok := modules[name]; !ok {
			return false
		}
	}
	return true
}
