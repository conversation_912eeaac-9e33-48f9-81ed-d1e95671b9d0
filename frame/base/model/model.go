package base_model

// BaseModelAPI 定义了模型的基本接口
type BaseModelAPI interface {
	Enabled() bool   // 返回模型是否启用
	Name() string    // 返回模型的名称
	Validate() error // 验证模型的有效性
}

// BaseModel 是模型的基础结构体
type BaseModel struct {
	UEnable bool   `toml:"enabled" default:"false"` // 是否启用
	UName   string `toml:"name" default:"default"`  // 模型名称
}

// Enabled 返回模型是否启用
func (um BaseModel) Enabled() bool {
	return um.UEnable
}

// Name 返回模型的名称
func (um BaseModel) Name() string {
	return um.UName
}

// Validate 验证模型的有效性，默认返回nil
func (um BaseModel) Validate() error {
	return nil
}

// BaseModel 是模型的基础结构体
type BaseEnabledModel struct {
	UEnable bool   `toml:"enabled" default:"true"` // 是否启用
	UName   string `toml:"name" default:"default"` // 模型名称
}

// Enabled 返回模型是否启用
func (um BaseEnabledModel) Enabled() bool {
	return um.UEnable
}

// Name 返回模型的名称
func (um BaseEnabledModel) Name() string {
	return um.UName
}

// Validate 验证模型的有效性，默认返回nil
func (um BaseEnabledModel) Validate() error {
	return nil
}
