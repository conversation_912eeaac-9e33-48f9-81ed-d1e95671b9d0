package base_model

import (
	"fmt"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"github.com/pelletier/go-toml"
)

// parseModelTree 解析 TOML 树并将其映射到 BaseModelAPI 类型的结构体中
// 参数:
//   - tree: 要解析的 TOML 树
//   - temp: 目标结构体指针
//
// 返回值:
//   - error: 解析过程中遇到的错误，如果没有错误则返回 nil
func parseModelTree[T BaseModelAPI](tree *toml.Tree, temp *T) error {
	err := tree.Unmarshal(temp)
	if err != nil {
		return err
	}

	if !(*temp).Enabled() {
		return nil
	}

	temp_name := (*temp).Name()
	if temp_name == "" {
		return fmt.Errorf("parse config: error: name can not be zero")
	}

	return (*temp).Validate()
}

// ParseModels 解析多个 TOML 树并生成 BaseModelAPI 类型的映射
// 参数:
//   - tt: TOML 树切片
//   - entity: 要解析的实体名称
//
// 返回值:
//   - map[string]*T: 解析后的模型映射，键为模型名称
//   - error: 解析过程中遇到的错误，如果没有错误则返回 nil
func ParseModels[T BaseModelAPI](t *toml.Tree, entity string) (map[string]*T, error) {
	listv := t.GetArray(entity)
	treeList, ok := listv.([]*toml.Tree)

	if !ok {
		//说明没注册信息
		if bif.InterfaceIsNil(treeList) {
			return nil, nil
		}

		return nil, fmt.Errorf("%s register multi models, but config is not toml list:[[%s]]", entity, entity)
	}

	results := make(map[string]*T)
	for _, v := range treeList {
		temp := new(T)
		if err := parseModelTree(v, temp); err != nil {
			return nil, err
		}

		if !(*temp).Enabled() {
			continue
		}

		temp_name := (*temp).Name()
		if _, ok := results[temp_name]; ok {
			return nil, fmt.Errorf("parse config:%s, repeat name %s", entity, temp_name)
		}

		results[temp_name] = temp
	}

	return results, nil
}

// ParseModel 解析 TOML 树并生成单个 BaseModelAPI 类型的实例
// 参数:
//   - tt: TOML 树切片
//   - entity: 要解析的实体名称
//
// 返回值:
//   - *T: 解析后的模型实例
//   - error: 解析过程中遇到的错误，如果没有错误则返回 nil
func ParseModel[T BaseModelAPI](t *toml.Tree, entity string) (*T, error) {
	entity_value := t.Get(entity)
	if entity_value == nil {
		return nil, nil
	}

	treeValue, ok := entity_value.(*toml.Tree)
	if !ok {
		return nil, fmt.Errorf("%s register singleton model, but config is not toml object:[%s]", entity, entity)
	}

	temp := new(T)
	if err := parseModelTree(treeValue, temp); err != nil {
		return nil, err
	}

	if !(*temp).Enabled() {
		return nil, nil
	}

	return temp, nil
}
