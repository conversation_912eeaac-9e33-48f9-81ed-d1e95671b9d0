package datatool

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/expr-lang/expr"
	"github.com/expr-lang/expr/vm"
)

// RankExpr 表达式
type RankExpr struct {
	truncateFunc expr.Option // 截取函数
	sprintfFunc  expr.Option // 字符串格式化函数
	programCache *LRUCache   // 缓存编译好的表达式
}

// NewRankExpr 创建表达式
func NewRankExpr() *RankExpr {
	return &RankExpr{
		programCache: NewLRUCache(10),
		truncateFunc: expr.Function(
			"Sprintf",
			func(params ...any) (any, error) {
				return fmt.Sprintf(params[0].(string), params[1:]...), nil
			},
			fmt.Sprintf,
		),
		sprintfFunc: expr.Function(
			"Truncate",
			func(params ...any) (any, error) {
				return TruncateText(params[0].(string), params[1].(int)), nil
			},
			TruncateText,
		),
	}
}

// Run 执行表达式
func (r *RankExpr) Run(code string, env any) (any, error) {
	// 尝试从缓存中获取编译好的程序
	if program, ok := r.programCache.Get(code); ok {
		return expr.Run(program.(*vm.Program), env)
	}

	// 编译代码并存入缓存
	program, err := expr.Compile(code, expr.Env(env), r.sprintfFunc, r.truncateFunc, expr.AsKind(reflect.String))
	if err != nil {
		return nil, err
	}
	r.programCache.Put(code, program)

	return expr.Run(program, env)
}

// TruncateText 按照字符长度截取字符串
func TruncateText(text string, maxLen int) string {
	// Content前后去空格
	text = strings.TrimSpace(text)

	// 转换成rune
	contentRune := []rune(text)
	limit := len(contentRune)
	if limit > maxLen {
		limit = maxLen
	}
	return string(contentRune[:limit])
}
