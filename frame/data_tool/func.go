package datatool

import (
	"crypto/rand"
	"encoding/hex"
	"math"
)

// GenerateRandomString 生成随机字符串
func GenerateRandomString(n int) string {
	bytes := make([]byte, n)
	if _, err := rand.Read(bytes); err != nil {
		return ""
	}
	return hex.EncodeToString(bytes)[:n]
}

// Repeat repeat操作
func Repeat[T any](s T, n int) []T {
	res := make([]T, n)
	for i := 0; i < n; i++ {
		res[i] = s
	}
	return res
}

// Sigmoid sigmoid操作
func Sigmoid(x []float32) []float32 {
	for i := range x {
		x[i] = float32(1 / (1 + math.Exp(-float64(x[i]))))
	}
	return x
}

// Softmax softmax操作
func Softmax(x []float32) []float32 {
	var max float32
	for _, v := range x {
		if v > max {
			max = v
		}
	}
	var sum float32
	for i := range x {
		x[i] = float32(math.Exp(float64(x[i] - max)))
		sum += x[i]
	}
	for i := range x {
		x[i] /= sum
	}
	return x
}
