package datatool

import (
	"container/list"
	"sync"
)

// LRUCache represents a thread-safe LRU cache.
type LRUCache struct {
	capacity int
	cache    map[string]*list.Element
	list     *list.List
	lock     sync.RWMutex
}

// entry is the type stored in each element of the list.
type entry struct {
	key   string
	value any
}

// NewLRUCache creates a new LRUCache with the given capacity.
func NewLRUCache(capacity int) *LRUCache {
	return &LRUCache{
		capacity: capacity,
		cache:    make(map[string]*list.Element),
		list:     list.New(),
	}
}

// Get retrieves a value from the cache.
func (l *LRUCache) Get(key string) (any, bool) {
	l.lock.RLock()
	defer l.lock.RUnlock()

	if elem, ok := l.cache[key]; ok {
		l.list.MoveToFront(elem)
		return elem.Value.(*entry).value, true
	}
	return nil, false
}

// Put adds a value to the cache, evicting the least recently used item if necessary.
func (l *LRUCache) Put(key string, value any) {
	l.lock.Lock()
	defer l.lock.Unlock()

	if elem, ok := l.cache[key]; ok {
		l.list.MoveToFront(elem)
		elem.Value.(*entry).value = value
		return
	}

	if l.list.Len() >= l.capacity {
		lastElem := l.list.Back()
		if lastElem != nil {
			l.list.Remove(lastElem)
			delete(l.cache, lastElem.Value.(*entry).key)
		}
	}

	newElem := l.list.PushFront(&entry{key: key, value: value})
	l.cache[key] = newElem
}
