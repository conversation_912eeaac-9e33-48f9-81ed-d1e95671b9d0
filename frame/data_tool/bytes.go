package datatool

import (
	"reflect"
	"unsafe"

	"golang.org/x/exp/constraints"
)

func sizeOf[T constraints.Ordered]() int {
	// Sadly, we can't do type assertions to get underlying types, so we need
	// to use reflect here instead.
	var v T
	return int(reflect.TypeOf(v).Size())
}

// SliceToBytes 切片转字节
func SliceToBytes[T constraints.Ordered](in []T) []byte {
	size := sizeOf[T]()
	floatPtr := unsafe.Pointer(&in[0])
	// 使用 unsafe.Slice 将 []float32 转换为 []byte
	byteArray := unsafe.Slice((*byte)(floatPtr), len(in)*size)

	return byteArray
}

// BytesToSlice 字节转切片
func BytesToSlice[T constraints.Ordered](in []byte) []T {
	size := sizeOf[T]()
	floatPtr := unsafe.Pointer(&in[0])
	// 使用 unsafe.Slice 将 []byte 转换为 []float32
	floatArray := unsafe.Slice((*T)(floatPtr), len(in)/size)

	return floatArray
}
