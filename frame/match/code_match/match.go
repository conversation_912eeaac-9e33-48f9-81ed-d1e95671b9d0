package code_match

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/builder"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/match/match_context"
)

type MatchExcuteFn = CodeExcuteFn

type matchExcute struct {
	bi         *builder.Builder[CodeMatch]
	mfn        map[any]MatchExcuteFn
	fallBackFn MatchExcuteFn
}

func newMatchExcute(p *CodeMatch) matchExcute {
	return matchExcute{
		bi:  builder.NewBuilderPtr[CodeMatch](p),
		mfn: make(map[any]MatchExcuteFn),
	}
}

func (m *matchExcute) Match(key any, value MatchExcuteFn) *CodeMatch {
	m.mfn[key] = value
	return m.bi.GetBuilder()
}

// 当所有策略匹配不上时，使用fallback 进行兜底
func (m *matchExcute) FallBack(value MatchExcuteFn) *CodeMatch {
	m.fallBackFn = value
	return m.bi.GetBuilder()
}

func (m *matchExcute) excute(fc *match_context.MatchContext, key any) bool {
	if fn, ok := m.mfn[key]; ok {
		fn(fc)
		return true
	} else {
		if m.fallBackFn != nil {
			m.fallBackFn(fc)
			return true
		}
	}

	return false
}

func (m *matchExcute) Exist(key any) bool {
	if _, ok := m.mfn[key]; ok {
		return true
	}
	return false
}
