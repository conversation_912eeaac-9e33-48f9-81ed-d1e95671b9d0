package code_match

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/builder"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/match/match_context"
)

type RecoverExcuteFn func(fc *match_context.MatchContext, recover any)

type recoverExcute struct {
	bi *builder.Builder[CodeMatch]
	fn RecoverExcuteFn
}

func newRecoverExcute(p *CodeMatch) recoverExcute {
	return recoverExcute{
		bi: builder.NewBuilderPtr[CodeMatch](p),
	}
}

func (p *recoverExcute) Recover(fn RecoverExcuteFn) *CodeMatch {
	p.fn = fn
	return p.bi.GetBuilder()
}

func (p *recoverExcute) excute(fc *match_context.MatchContext, recover any) {
	if p.fn != nil {
		p.fn(fc, recover)
	}
	return
}
