package boot_gzip

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"io"
	"time"
)

func Compress(data []byte) ([]byte, int64, error) {
	st := time.Now()
	var compressedData bytes.Buffer
	// Create a new GZIP writer with the default compression level
	writer := gzip.NewWriter(&compressedData)

	// Write data to the GZIP writer
	_, err := writer.Write(data)
	if err != nil {
		return nil, int64(time.Now().Sub(st).Milliseconds()), fmt.Errorf("gzip compress fail:%s", err.Error())
	}

	writer.Close()

	// Return the compressed data
	return compressedData.Bytes(), int64(time.Now().Sub(st).Milliseconds()), nil
}

// Function to decompress data using GZIP
func Depress(data []byte) ([]byte, int64, error) {
	st := time.Now()
	// Create a new GZIP reader
	reader, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, int64(time.Now().Sub(st).Milliseconds()), fmt.Errorf("gzip depress fail:%s", err.Error())
	}
	reader.Close()

	// Read the decompressed data
	buf, err := io.ReadAll(reader)
	if err != nil {
		return nil, int64(time.Now().Sub(st).Milliseconds()), fmt.Errorf("gzip depress fail:%s", err.Error())
	}

	return buf, int64(time.Now().Sub(st).Milliseconds()), nil
}
