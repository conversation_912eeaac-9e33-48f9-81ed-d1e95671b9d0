package conns_pool

import (
	"context"
	"errors"
	"sync"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/structure/unsafe/collections/bslice"
)

type connList[T any] struct {
	last      time.Time
	addr      string
	opt       *Option
	rwlock    sync.RWMutex
	connsHeap []*connState[T]
	factory   Factory[T]
}

func newConnList[T any](addr string, opt *Option, factory Factory[T]) *connList[T] {
	return &connList[T]{
		last:      time.Now(),
		addr:      addr,
		opt:       opt,
		rwlock:    sync.RWMutex{},
		connsHeap: make([]*connState[T], 0, 32),
		factory:   factory,
	}
}

func (cl *connList[T]) GetConn(ctx context.Context, addr string) (*connState[T], error) {
	cl.rwlock.Lock()
	defer cl.rwlock.Unlock()

	cl.last = time.Now()

	//查找可用的链接
	for i, v := range cl.connsHeap {
		//acquire 成功
		if v.acquire() {
			return cl.connsHeap[i], nil
		}
	}

	//找不到，则添加
	return cl.addConn(ctx, addr)
}

func (cl *connList[T]) lastTime() time.Time {
	cl.rwlock.RLock()
	defer cl.rwlock.RUnlock()
	return cl.last
}

func (cl *connList[T]) addConn(ctx context.Context, addr string) (*connState[T], error) {

	//超过限制
	if len(cl.connsHeap) >= cl.opt.MaxConnNumPerHost {
		return nil, errors.New("exceed max conn per host")
	}

	conn, err := cl.factory.Connect(ctx, addr)
	if err != nil {
		return nil, err
	}

	cs := newConnState(addr, conn, cl.opt)
	cl.connsHeap = append(cl.connsHeap, cs)
	return cs, nil
}
func (cl *connList[T]) GetAll() []*connState[T] {
	cl.rwlock.RLock()
	defer cl.rwlock.RUnlock()

	return bslice.Copy(cl.connsHeap)
}

func (cl *connList[T]) remove(server *connState[T]) {
	cl.rwlock.Lock()
	defer cl.rwlock.Unlock()

	bslice.Delete(&cl.connsHeap, server)
	return
}
