package conns_pool

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/internal/console"
)

type ConnCall[T any] func(ctx context.Context, addr string, conn *T) (any, error)

// pool功能：
// 设置每个host地址上的最大连接数，如果不超过该值，则可用复用；
// 如果超过该值，则可用创建新的连接，但是不能超过最大连接数；
// 支持连接回收。
type ConnPool[T any] struct {
	option *Option
	m      *connsCache[T]
	ctx    context.Context
	cancel context.CancelFunc
}

var debug = false

func NewConnsPool[T any](factory Factory[T], fns ...OptionCall) (*ConnPool[T], error) {
	opt := newOption()

	for _, fn := range fns {
		fn(opt)
	}

	err := opt.validate()
	if err != nil {
		return nil, err
	}

	if debug {
		oss, _ := json.Marshal(opt)
		console.Console("conns pool option", string(oss))
	}

	ctx, cancel := context.WithCancel(context.TODO())
	cp := &ConnPool[T]{
		ctx:    ctx,
		cancel: cancel,
		option: opt,
		m:      newConnsCache[T](opt, factory),
	}

	go cp.clean()
	return cp, nil
}

func (cp *ConnPool[T]) Call(ctx context.Context, addr string, fn ConnCall[T]) (out any, err error) {
	var cs *connState[T]

	defer func() {
		if errp := recover(); errp != nil {
			console.PanicStack()
			err = errors.New("panic")
		}

		//释放权重
		if cs != nil {
			cs.release()
		}
	}()

	clists := cp.m.getOrNew(addr)

	cs, err = clists.GetConn(ctx, addr)
	if err != nil {
		return nil, err
	}

	return fn(ctx, addr, cs.conn)
}

func (cp *ConnPool[T]) Close() {
	cp.m.clean()
	cp.cancel()
}

func (cp *ConnPool[T]) clean() {

	t := time.NewTicker(cp.option.CleanInterval)

	for {
		bexit := false
		select {
		case <-t.C:
			addrs := cp.m.getKeys()

			if debug {
				fmt.Println("conn clean", addrs)
			}

			if len(addrs) == 0 {
				continue
			}

			for _, addr := range addrs {
				cl := cp.m.get(addr)

				if debug {
					fmt.Println("conn clean", addrs, len(cl.GetAll()))
				}

				//判断清理cache,某个addr长时间不用可能已经下线了。
				e := time.Since(cl.lastTime())
				if e > cp.option.MaxIdleTime {
					cns := cp.m.remove(addr)

					if debug {
						fmt.Println("conn clean addrs cache", addr, e, cp.option.MaxIdleTime)
					}

					if cns == nil {
						continue
					}

					css := cns.GetAll()

					if css == nil {
						continue
					}

					for i, _ := range css {
						cl.remove(css[i])
						cl.factory.Close(addr, css[i].conn)
					}

					continue
				}

				css := cl.GetAll()
				num := len(css)
				if num == 0 || num <= cp.option.MaxIdleNum {
					continue
				}

				for i, cs := range css {
					e := time.Since(cs.lastTime())
					if e > cp.option.MaxIdleTime {
						cl.remove(css[i])
						cl.factory.Close(addr, css[i].conn)
					}

					if num <= cp.option.MaxIdleNum {
						break
					}
				}

				if debug {
					console.Console("conn pool clean "+addr, fmt.Sprintf("%d", len(cp.m.get(addr).GetAll())))
				}
			}
		case <-cp.ctx.Done():
			bexit = true
		}

		if bexit {
			break
		}
	}

	console.Console("conns pool", "exit")
	cp.Close()

	time.Sleep(3 * time.Second)
}
