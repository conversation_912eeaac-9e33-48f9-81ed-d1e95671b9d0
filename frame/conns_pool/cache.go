package conns_pool

import (
	"sync"
)

type connsCache[T any] struct {
	rwlock sync.RWMutex
	opt    *Option
	f      Factory[T]
	m      map[string]*connList[T]
}

func newConnsCache[T any](opt *Option, f Factory[T]) *connsCache[T] {
	return &connsCache[T]{
		rwlock: sync.RWMutex{},
		f:      f,
		opt:    opt,
		m:      map[string]*connList[T]{},
	}
}

// get 不到的时候再new,new的时候可能已经有值,再做一次判断
// 大部分场景只会命中get
func (cce *connsCache[T]) getOrNew(addr string) *connList[T] {
	cl := cce.get(addr)
	if cl != nil {
		return cl
	}

	return cce.new(addr)
}

func (cce *connsCache[T]) get(addr string) *connList[T] {
	cce.rwlock.RLock()
	defer cce.rwlock.RUnlock()

	return cce.m[addr]
}

func (cce *connsCache[T]) remove(addr string) *connList[T] {
	cce.rwlock.Lock()
	defer cce.rwlock.Unlock()

	cl := cce.m[addr]
	delete(cce.m, addr)
	return cl
}

func (cce *connsCache[T]) new(addr string) *connList[T] {
	cce.rwlock.Lock()
	defer cce.rwlock.Unlock()

	_, ok := cce.m[addr]
	if ok {
		return cce.m[addr]
	}

	cce.m[addr] = newConnList(addr, cce.opt, cce.f)
	return cce.m[addr]
}

func (cce *connsCache[T]) getKeys() []string {
	cce.rwlock.RLock()
	defer cce.rwlock.RUnlock()

	keys := make([]string, 0, len(cce.m))

	for key, _ := range cce.m {
		keys = append(keys, key)
	}
	return keys
}

func (cce *connsCache[T]) clean() {
	cce.rwlock.Lock()
	defer cce.rwlock.Unlock()
	cce.m = nil
	return
}
