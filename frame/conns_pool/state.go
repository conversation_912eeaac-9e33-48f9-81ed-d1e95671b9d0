package conns_pool

import (
	"sync"
	"time"
)

type connState[T any] struct {
	opt   *Option
	addr  string
	last  time.Time
	tlock sync.RWMutex
	conn  *T

	load int
}

func newConnState[T any](addr string, conn *T, opt *Option) *connState[T] {
	return &connState[T]{
		opt:   opt,
		last:  time.Now(),
		tlock: sync.RWMutex{},
		addr:  addr,
		conn:  conn,
		load:  1,
	}
}

func (s *connState[T]) lastTime() time.Time {
	s.tlock.RLock()
	defer s.tlock.RUnlock()
	return s.last
}

func (s *connState[T]) acquire() bool {
	s.tlock.Lock()
	defer s.tlock.Unlock()
	s.last = time.Now()

	if s.load >= s.opt.MaxLoadNumPerConn {
		return false
	}
	s.load += 1
	return true
}

func (s *connState[T]) release() {
	s.tlock.Lock()
	defer s.tlock.Unlock()
	s.last = time.Now()
	s.load -= 1
	if s.load < 0 {
		s.load = 0
	}
	return
}
