package conns_pool

import (
	"errors"
	"math"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
)

type OptionCall func(o *Option)

type Option struct {
	MaxLoadNumPerConn int           //单Connection上的并发数，若=1,则不复用；必须大于0。
	MaxConnNumPerHost int           //单个host上的最大Connection数，小于该值，可一直创建；必须大于0。
	MaxIdleNum        int           //最大空闲数，程序启动时会预创建出该数值。若为0，则不保留任何链接。
	MaxIdleTime       time.Duration //最大空闲时间，必须大于0。
	CleanInterval     time.Duration //清理周期
}

func (o *Option) validate() error {
	bf := bif.NewOnceIf()

	bf.If(o.MaxLoadNumPerConn <= 0, errors.New("invalid MaxLoadNumPerConn"))
	bf.If(o.MaxConnNumPerHost <= 0, errors.New("invalid MaxConnNumPerHost"))
	bf.If(o.MaxIdleNum < 0, errors.New("invalid MaxIdleNum"))
	bf.If(o.MaxIdleTime <= 0, errors.New("invalid MaxIdleTime"))
	bf.If(o.CleanInterval <= 0, errors.New("invalid CleanInterval"))

	return bf.Error()
}

const (
	defaultMaxLoadNumPerConn = 100
	defaultMaxConnNumPerHost = math.MaxInt
	defaultMaxIdleNum        = 1
	defaultMaxIdleTime       = time.Duration(300) * time.Second
	defaultCleanInterval     = time.Duration(60) * time.Second
)

func newOption() *Option {
	return &Option{
		MaxLoadNumPerConn: defaultMaxLoadNumPerConn,
		MaxIdleNum:        defaultMaxIdleNum,
		MaxConnNumPerHost: defaultMaxConnNumPerHost,
		MaxIdleTime:       defaultMaxIdleTime,
		CleanInterval:     defaultCleanInterval,
	}
}

func WithMaxLoadNumPerConn(num int) OptionCall {
	return func(o *Option) {
		o.MaxLoadNumPerConn = num
	}
}

func WithMaxIdleNum(num int) OptionCall {
	return func(o *Option) {
		o.MaxIdleNum = num
	}
}

func WithMaxConnNumPerHost(num int) OptionCall {
	return func(o *Option) {
		o.MaxConnNumPerHost = num
	}
}

func WithMaxIdleTime(num time.Duration) OptionCall {
	return func(o *Option) {
		o.MaxIdleTime = num
	}
}

func WithCleanInterval(num time.Duration) OptionCall {
	return func(o *Option) {
		o.CleanInterval = num
	}
}
