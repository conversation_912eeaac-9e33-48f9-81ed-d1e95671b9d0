package concurrent_map

import "sync"

// ConcurrentMap 是一个支持并发读写的泛型映射结构
type ConcurrentMap[K comparable, V any] struct {
	lock  sync.RWMutex  // 读写锁，用于保护mapkv的并发访问
	mapkv map[K]V       // 存储键值对的映射
}

// NewConcurrentMap 创建一个新的ConcurrentMap实例
func New[K comparable, V any]() *ConcurrentMap[K, V] {
	cm := &ConcurrentMap[K, V]{
		lock:  sync.RWMutex{},  // 初始化读写锁
		mapkv: map[K]V{},       // 初始化映射
	}

	return cm
}

// read 方法在读锁保护下执行传入的函数
func (cm *ConcurrentMap[K, V]) read(yield func(m map[K]V) ) {
	cm.lock.RLock()           // 加读锁
	defer cm.lock.RUnlock()   // 释放读锁
	yield(cm.mapkv)           // 执行传入的函数
}

// write 方法在写锁保护下执行传入的函数
func (cm *ConcurrentMap[K, V]) write(yield func(m map[K]V) ) {
	cm.lock.Lock()           // 加写锁
	defer cm.lock.Unlock()   // 释放写锁
	yield(cm.mapkv)          // 执行传入的函数
}