package concurrent_map

import (
	"maps"
	"sync"
)

// Set 方法用于将键值对存储到并发映射中
func (cm *ConcurrentMap[K, V]) Set(key K, value V) {
	cm.write(func(m map[K]V) {
		m[key] = value
	})
}

// Get 方法用于从并发映射中获取指定键的值，并返回是否存在该键
func (cm *ConcurrentMap[K, V]) Get(key K) (V, bool) {
	var v V
	var ok bool = false

	cm.read(func(m map[K]V) {	
		v, ok = m[key]
	})

	return v, ok
}


func (cm *ConcurrentMap[K, V]) Delete(key K) {
	cm.write(func(m map[K]V) {	
		delete(m, key)
	})
}

// Update 方法用于在写锁保护下更新并发映射中的内容
func (cm *ConcurrentMap[K, V]) UpdateFunc(yield func(m map[K]V)) {
	cm.write(yield)
}

// Search 方法用于在读锁保护下搜索并发映射中的内容
func (cm *ConcurrentMap[K, V]) SearchFunc(yield func(m map[K]V)) {
	cm.read(yield)
}

// Keys 方法用于获取并发映射中的所有键
func (cm *ConcurrentMap[K, V]) GetKeys() []K {
	keys := make([]K, 0, len(cm.mapkv))
	cm.read(func(m map[K]V) {
		for k := range m {
			keys = append(keys, k)
		}
	})
	return keys
}

// Values 方法用于获取并发映射中的所有值
func (cm *ConcurrentMap[K, V]) GetValues() []V {
	values := make([]V, 0, len(cm.mapkv))

	cm.read(func(m map[K]V) {
		for k := range m {
			values = append(values, cm.mapkv[k])
		}
	})

	return values
}

// Copy 方法用于创建并发映射的浅拷贝
func (cm *ConcurrentMap[K, V]) Copy() *ConcurrentMap[K, V] {
	results := &ConcurrentMap[K, V]{
		lock:  sync.RWMutex{},
		mapkv: map[K]V{},
	}
	cm.read(func(m map[K]V) {
		maps.Copy(results.mapkv, m)
	})
	return results
}

// Clone 方法用于创建并发映射的深拷贝
func (cm *ConcurrentMap[K, V]) Clone() *ConcurrentMap[K, V] {
	results := &ConcurrentMap[K, V]{
		lock:  sync.RWMutex{},
		mapkv: map[K]V{},
	}

	cm.read(func(m map[K]V) {
		results.mapkv = maps.Clone(m)
	})

	return results
}

func (cm *ConcurrentMap[K, V]) Has(key K) bool {
	var has bool = false
	cm.read(func(m map[K]V) {
		_, ok := m[key]
		has = ok
	})

	return has
}

func (cm *ConcurrentMap[K, V]) InnnerCopy() map[K]V {
	mapkv := map[K]V{}

	cm.read(func(m map[K]V) {
		maps.Copy(mapkv,m)
	})

	return mapkv
}

func (cm *ConcurrentMap[K, V]) InnnerClone() map[K]V {
	var mapkv map[K]V

	cm.read(func(m map[K]V) {
		mapkv = maps.Clone(m)
	})

	return mapkv
}