package concurrent

import (
	concurrent_map "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/concurrent/map"
	concurrent_slice "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/concurrent/slice"
)

func NewSlice[V any](length, cap int) *concurrent_slice.ConcurrentSlice[V] {
	return concurrent_slice.New[V](length, cap)
}


func NewMap[K comparable, V any]() *concurrent_map.ConcurrentMap[K, V] {
	return concurrent_map.New[K, V]()
}
