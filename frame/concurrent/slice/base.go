package concurrent_slice

import (
	"sync"
)

type ConcurrentSlice[V any] struct {
	lock  sync.RWMutex
	sdatas []V
}

func New[ V any](length,cap int) *ConcurrentSlice[V] {
	cm := &ConcurrentSlice[V]{
		lock:  sync.RWMutex{},
		sdatas: make([]V, length,cap),
	}

	return cm
}

// read 方法在读锁保护下执行传入的函数
func (cm *ConcurrentSlice[V]) read(yield func(s []V)) {
	cm.lock.RLock()           // 加读锁
	defer cm.lock.RUnlock()   // 释放读锁
	yield(cm.sdatas)           // 执行传入的函数
}

// write 方法在写锁保护下执行传入的函数
func (cm *ConcurrentSlice[V]) write(yield func(s []V) []V) {
	cm.lock.Lock()           // 加写锁
	defer cm.lock.Unlock()   // 释放写锁
	cm.sdatas = yield(cm.sdatas)          // 执行传入的函数
}


