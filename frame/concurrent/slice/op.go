package concurrent_slice

import (
	"slices"
)

func (cm *ConcurrentSlice[V]) At (i int) (V,bool) {
	var v V
	var ok bool 

	cm.read(func(m []V)  {
		if i < len(m) {
			v = m[i]
			ok = true
		}else {
			ok = false
		}
		return 
	})

	return v,ok
}

func (cm *ConcurrentSlice[V]) Append (data ...V) {
	cm.write(func(m []V) []V {
		m = append(m, data...)
		return m
	})
}

func (cm *ConcurrentSlice[V]) Delete(i,j int) {
	cm.write(func(s []V) []V {
		slices.Delete(s,i,j)
		return s
	})
}

// Update 方法用于在写锁保护下更新并发映射中的内容
func (cm *ConcurrentSlice[V]) UpdateFunc(yield func(m []V) []V) {
	cm.write(yield)
}

// Search 方法用于在读锁保护下搜索并发映射中的内容
func (cm *ConcurrentSlice[V]) SearchFunc(yield func(m []V)) {
	cm.read(yield)
}

func (cm *ConcurrentSlice[V]) Length() int {
	length := 0
	cm.read(func(m []V) {
		length = len(m)
	})
	return length
}

func (cm *ConcurrentSlice[V]) Cap() int {
	capv := 0
	cm.read(func(m []V) {
		capv = cap(m)
	})
	return capv
}

func (cm *ConcurrentSlice[V]) InnerClone() []V {
	var s []V
	cm.read(func(m []V) {
		s = slices.Clone(m)
	})
	return s
}


func (cm *ConcurrentSlice[V]) InnerCopy() []V {
	var s []V = make([]V, cm.Length())
	cm.read(func(m []V) {
		copy(s,m)
	})
	return s
}