package chains_engine

import "context"

type ChainsEngine interface {
	Register(phs ...*ProcessHandler) ChainsEngine
	Then(name string, fn ProcessHandlerFn) ChainsEngine
	ChainesLen() int
	Recover(rfn RecoverFn) ChainsEngine
	Final(fn FinalFn) ChainsEngine
	Catch(err error, fn CatchFn) ChainsEngine
	Error(fn ErrorFn) ChainsEngine
	StartFromContext(ctx context.Context) ChainsContext
	Start() ChainsContext
}

// ChainsEngine 是一个用于管理和执行处理链的引擎。
type chainsEngine struct {
	finalFn      FinalFn
	recoverFn    RecoverFn
	catchs       map[string]CatchFn
	errFn        ErrorFn
	engineChains []*ProcessHandler
}

// NewChainsEngine 创建一个新的 ChainsEngine 实例。
func NewChainsEngine() ChainsEngine {
	return &chainsEngine{
		engineChains: []*ProcessHandler{},
		catchs:       map[string]CatchFn{},
	}
}

// Register 将处理函数注册到指定名称的处理链中。
// chainName: 处理链的名称
// phs: 要注册的处理函数列表
// 返回 ChainsEngine 实例以便进行链式调用。
func (ce *chainsEngine) Register(phs ...*ProcessHandler) ChainsEngine {
	for k, _ := range phs {
		ce.engineChains = append(ce.engineChains, phs[k])
	}

	return ce
}

func (ce *chainsEngine) Then(name string, fn ProcessHandlerFn) ChainsEngine {
	ce.engineChains = append(ce.engineChains, &ProcessHandler{
		StepName: name,
		Handler:  fn,
	})

	return ce
}

func (ce *chainsEngine) ChainesLen() int {
	return len(ce.engineChains)
}

func (ce *chainsEngine) Recover(rfn RecoverFn) ChainsEngine {
	ce.recoverFn = rfn
	return ce
}

func (ce *chainsEngine) Final(fn FinalFn) ChainsEngine {
	ce.finalFn = fn
	return ce
}

func (ce *chainsEngine) Catch(err error, fn CatchFn) ChainsEngine {
	ce.catchs[err.Error()] = fn
	return ce
}

func (ce *chainsEngine) Error(fn ErrorFn) ChainsEngine {
	ce.errFn = fn
	return ce
}

func (ce *chainsEngine) StartFromContext(ctx context.Context) ChainsContext {
	ectx := NewChainsContextFromContext(ctx)
	return ectx.Start(ce)
}

func (ce *chainsEngine) Start() ChainsContext {
	ectx := NewChainsContextFromContext(context.TODO())
	return ectx.Start(ce)
}
