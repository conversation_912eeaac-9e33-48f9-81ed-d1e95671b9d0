package chains_engine

import (
	"context"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/structure/bcontext"
)

type ProcessHandlerFn func(ctx ChainsContext)
type RecoverFn func(ctx ChainsContext, recover any)
type FinalFn func(ctx ChainsContext)
type CatchFn func(ctx ChainsContext)
type ErrorFn func(err error, ctx ChainsContext)

// ProcessHandler 定义了处理链中每个处理函数的类型
type ProcessHandler struct {
	Handler  ProcessHandlerFn
	StepName string
}

func NewProcessHandler(stepName string, handler ProcessHandlerFn) ProcessHandler {
	return ProcessHandler{
		Handler:  handler,
		StepName: stepName,
	}
}

// ChainsContext 是处理链的上下文，包含了处理链的状态和存储// 原始上下文
type chainsContext struct {
	index    int // 当前处理函数的索引
	stepName string
	engine   *chainsEngine
	super    any
	bcontext.BContext
}

type ChainsContext interface {
	bcontext.BContext

	// Ctx 返回原始上下文
	Engine() ChainsEngine

	// IsError 检查上下文是否包含错误
	IsError() bool

	// Start 启动处理链并返回上下文
	Start(e ChainsEngine) ChainsContext

	// Next 执行处理链中的下一个处理函数
	Next()

	// Abort 终止处理链并记录错误
	Abort(err error)

	Throw(err error)
	SetSuper(super any) ChainsContext
	Super() any

	StepIndex() int

	StepName() string
}

func NewChainsContext() ChainsContext {
	return &chainsContext{
		index: -1,
		//store: map[any]any{},
		BContext: bcontext.NewBContext(),
	}

}

func NewChainsContextFromContext(ctx context.Context) ChainsContext {
	return &chainsContext{
		BContext: bcontext.NewBContextFromContext(ctx),
		index:    -1,
	}

}

// Ctx 返回原始上下文
func (ic *chainsContext) Engine() ChainsEngine {
	return ic.engine
}

// IsError 检查上下文是否包含错误
func (ic *chainsContext) IsError() bool {
	return ic.Error() != nil
}

// Start 启动处理链并返回上下文
func (ic *chainsContext) Start(e ChainsEngine) ChainsContext {
	v, ok := e.(*chainsEngine)
	if !ok {
		panic("invalid chains engine")
	}

	ic.engine = v

	defer func() {
		if ic.engine.recoverFn != nil {
			if r := recover(); r != nil {
				ic.engine.recoverFn(ic, r)
			}
		}
	}()

	ic.Next()

	if ic.Error() != nil {
		efn, ok := ic.engine.catchs[ic.Error().Error()]
		if ok {
			efn(ic)
		} else {
			if ic.engine.errFn != nil {
				ic.engine.errFn(ic.Error(), ic)
			}
		}
	}

	if ic.engine.finalFn != nil {
		ic.engine.finalFn(ic)
	}

	return ic
}

// Next 执行处理链中的下一个处理函数
func (ic *chainsContext) Next() {
	ic.index += 1
	for ic.index < len(ic.engine.engineChains) {
		ic.stepName = ic.engine.engineChains[ic.index].StepName
		ic.engine.engineChains[ic.index].Handler(ic)
		ic.index++
	}
}

// Abort 终止处理链并记录错误
func (ic *chainsContext) Abort(err error) {
	if err == nil {
		return
	}

	ic.index = len(ic.engine.engineChains)
	ic.SetError(err)
}

// Abort 终止处理链并记录错误
func (ic *chainsContext) Throw(err error) {
	ic.Abort(err)
}

func (ic *chainsContext) SetSuper(super any) ChainsContext {
	ic.super = super
	return ic
}

func (ic *chainsContext) Super() any {
	return ic.super
}

func (ic *chainsContext) StepIndex() int {
	return ic.index
}

func (ic *chainsContext) StepName() string {
	return ic.stepName
}
