package chains_engine

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
)

func CondError(cond bool, err error) ProcessHandlerFn {
	return func(fc ChainsContext) {
		fc.Abort(bif.Error(cond, err))
	}
}

func FuncError(f func() error) ProcessHandlerFn {
	return func(fc ChainsContext) {
		fc.Abort(f())
	}
}

func NeedError(f func(...string) error, name ...string) ProcessHandlerFn {
	return func(fc ChainsContext) {
		fc.Abort(f(name...))
	}
}
