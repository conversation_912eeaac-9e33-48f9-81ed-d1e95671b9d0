package chains_engine

// ChainsEngine 是一个用于管理和执行处理链的引擎。
type MultiChainsEngine struct {
	mengineChains map[string]ChainsEngine
}

// NewChainsEngine 创建一个新的 ChainsEngine 实例。
func NewMultiChainsEngine() *MultiChainsEngine {
	return &MultiChainsEngine{
		mengineChains: map[string]ChainsEngine{},
	}
}

// Register 将处理函数注册到指定名称的处理链中。
// chainName: 处理链的名称
// phs: 要注册的处理函数列表
// 返回 ChainsEngine 实例以便进行链式调用。
func (ce *MultiChainsEngine) Register(chainName string, phs ...*ProcessHandler) *MultiChainsEngine {
	_, ok := ce.mengineChains[chainName]
	if !ok {
		ce.mengineChains[chainName] = NewChainsEngine()
	}

	ce.mengineChains[chainName].Register(phs...)

	return ce
}

func (ce *MultiChainsEngine) ChainsEngine(chainName string) ChainsEngine {
	return ce.mengineChains[chainName]
}
